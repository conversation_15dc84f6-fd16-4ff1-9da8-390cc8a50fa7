/**
 * 基础类定义
 * 提供通用的基础类和接口
 */

import { EventEmitter, type IEventEmitter } from '../events';
import { generateId } from '../utils';

// ==================== 基础类接口 ====================

/**
 * 可销毁接口
 */
export interface IDisposable {
  /** 是否已销毁 */
  readonly disposed: boolean;
  /** 销毁方法 */
  dispose(): void;
}

/**
 * 可序列化接口
 */
export interface ISerializable<T = any> {
  /** 序列化为JSON */
  toJSON(): T;
  /** 从JSON反序列化 */
  fromJSON(data: T): void;
}

/**
 * 可克隆接口
 */
export interface ICloneable<T = any> {
  /** 克隆对象 */
  clone(): T;
}

/**
 * 可验证接口
 */
export interface IValidatable {
  /** 验证对象 */
  validate(): { valid: boolean; errors: string[] };
}

// ==================== 基础抽象类 ====================

/**
 * 基础对象类
 * 提供ID、事件、销毁等基础功能
 */
export abstract class BaseObject implements IDisposable {
  /** 对象唯一标识 */
  public readonly id: string;
  
  /** 对象名称 */
  public name: string;
  
  /** 创建时间 */
  public readonly createdAt: number;
  
  /** 更新时间 */
  public updatedAt: number;
  
  /** 是否已销毁 */
  public disposed = false;
  
  /** 事件发射器 */
  protected eventEmitter: IEventEmitter;
  
  constructor(id?: string, name?: string) {
    this.id = id || generateId();
    this.name = name || this.id;
    this.createdAt = Date.now();
    this.updatedAt = this.createdAt;
    this.eventEmitter = new EventEmitter();
  }
  
  /**
   * 更新对象
   */
  protected update(): void {
    this.updatedAt = Date.now();
  }
  
  /**
   * 销毁对象
   */
  dispose(): void {
    if (this.disposed) return;
    
    this.disposed = true;
    this.eventEmitter.clear();
    this.onDispose();
  }
  
  /**
   * 销毁时的钩子方法
   */
  protected onDispose(): void {
    // 子类可以重写此方法
  }
  
  /**
   * 获取对象信息
   */
  getInfo(): { id: string; name: string; createdAt: number; updatedAt: number } {
    return {
      id: this.id,
      name: this.name,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

/**
 * 可配置的基础对象类
 */
export abstract class ConfigurableObject<T = Record<string, any>> extends BaseObject 
  implements ISerializable<T>, IValidatable {
  
  /** 配置对象 */
  protected config: T;
  
  constructor(config: T, id?: string, name?: string) {
    super(id, name);
    this.config = { ...config };
  }
  
  /**
   * 获取配置
   */
  getConfig(): T {
    return { ...this.config };
  }
  
  /**
   * 设置配置
   */
  setConfig(config: Partial<T>): void {
    this.config = { ...this.config, ...config };
    this.update();
    this.onConfigChanged();
  }
  
  /**
   * 配置变更时的钩子方法
   */
  protected onConfigChanged(): void {
    // 子类可以重写此方法
  }
  
  /**
   * 序列化为JSON
   */
  toJSON(): T {
    return {
      ...this.config,
      id: this.id,
      name: this.name,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    } as T;
  }
  
  /**
   * 从JSON反序列化
   */
  fromJSON(data: T): void {
    const { id, name, createdAt, updatedAt, ...config } = data as any;
    this.config = config;
    this.name = name || this.name;
    this.updatedAt = updatedAt || this.updatedAt;
  }
  
  /**
   * 验证配置
   */
  validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // 基础验证
    if (!this.id) {
      errors.push('ID不能为空');
    }
    
    if (!this.name) {
      errors.push('名称不能为空');
    }
    
    // 调用子类验证
    const customErrors = this.validateConfig();
    errors.push(...customErrors);
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * 验证配置的抽象方法
   */
  protected abstract validateConfig(): string[];
}

// ==================== 集合管理类 ====================

/**
 * 对象集合管理器
 */
export class ObjectCollection<T extends BaseObject> implements IDisposable {
  /** 对象映射表 */
  private items = new Map<string, T>();
  
  /** 是否已销毁 */
  public disposed = false;
  
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();
  
  /**
   * 添加对象
   */
  add(item: T): void {
    if (this.disposed) {
      throw new Error('Collection has been disposed');
    }
    
    this.items.set(item.id, item);
    this.eventEmitter.emit('collection:item:added' as any, { item });
  }
  
  /**
   * 移除对象
   */
  remove(id: string): boolean {
    if (this.disposed) {
      throw new Error('Collection has been disposed');
    }
    
    const item = this.items.get(id);
    if (item) {
      this.items.delete(id);
      this.eventEmitter.emit('collection:item:removed' as any, { item });
      return true;
    }
    return false;
  }
  
  /**
   * 获取对象
   */
  get(id: string): T | undefined {
    return this.items.get(id);
  }
  
  /**
   * 检查是否存在
   */
  has(id: string): boolean {
    return this.items.has(id);
  }
  
  /**
   * 获取所有对象
   */
  getAll(): T[] {
    return Array.from(this.items.values());
  }
  
  /**
   * 获取对象数量
   */
  size(): number {
    return this.items.size;
  }
  
  /**
   * 清空集合
   */
  clear(): void {
    const items = this.getAll();
    this.items.clear();
    this.eventEmitter.emit('collection:cleared' as any, { items });
  }
  
  /**
   * 查找对象
   */
  find(predicate: (item: T) => boolean): T | undefined {
    for (const item of this.items.values()) {
      if (predicate(item)) {
        return item;
      }
    }
    return undefined;
  }
  
  /**
   * 过滤对象
   */
  filter(predicate: (item: T) => boolean): T[] {
    return this.getAll().filter(predicate);
  }
  
  /**
   * 遍历对象
   */
  forEach(callback: (item: T, id: string) => void): void {
    this.items.forEach((item, id) => callback(item, id));
  }
  
  /**
   * 监听事件
   */
  on(event: string, handler: Function): () => void {
    return this.eventEmitter.on(event as any, handler as any);
  }
  
  /**
   * 销毁集合
   */
  dispose(): void {
    if (this.disposed) return;
    
    this.disposed = true;
    
    // 销毁所有对象
    this.items.forEach(item => {
      if (!item.disposed) {
        item.dispose();
      }
    });
    
    this.items.clear();
    this.eventEmitter.clear();
  }
}

// ==================== 工厂模式基类 ====================

/**
 * 抽象工厂类
 */
export abstract class AbstractFactory<T, C = any> {
  /** 注册的创建器映射 */
  protected creators = new Map<string, (config: C) => T>();
  
  /**
   * 注册创建器
   */
  register(type: string, creator: (config: C) => T): void {
    this.creators.set(type, creator);
  }
  
  /**
   * 取消注册
   */
  unregister(type: string): boolean {
    return this.creators.delete(type);
  }
  
  /**
   * 创建对象
   */
  create(type: string, config: C): T {
    const creator = this.creators.get(type);
    if (!creator) {
      throw new Error(`Unknown type: ${type}`);
    }
    return creator(config);
  }
  
  /**
   * 获取所有注册的类型
   */
  getTypes(): string[] {
    return Array.from(this.creators.keys());
  }
  
  /**
   * 检查类型是否已注册
   */
  hasType(type: string): boolean {
    return this.creators.has(type);
  }
}

// ==================== 单例模式基类 ====================

/**
 * 单例基类
 */
export abstract class Singleton {
  private static instances = new Map<any, any>();
  
  constructor() {
    const constructor = this.constructor;
    if (Singleton.instances.has(constructor)) {
      return Singleton.instances.get(constructor);
    }
    Singleton.instances.set(constructor, this);
  }
  
  /**
   * 获取实例
   */
  static getInstance<T extends Singleton>(this: new () => T): T {
    if (!Singleton.instances.has(this)) {
      new this();
    }
    return Singleton.instances.get(this);
  }
}
