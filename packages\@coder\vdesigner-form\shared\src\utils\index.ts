/**
 * 共享工具函数
 */

import { nanoid } from 'nanoid';
import { cloneDeep, isEqual, merge } from 'lodash-es';

// ==================== ID 生成工具 ====================

/**
 * 生成唯一ID
 */
export const generateId = (prefix?: string): string => {
  const id = nanoid(8);
  return prefix ? `${prefix}_${id}` : id;
};

/**
 * 生成Widget ID
 */
export const generateWidgetId = (type: string): string => {
  return generateId(`widget_${type}`);
};

/**
 * 生成数据源ID
 */
export const generateDataSourceId = (name: string): string => {
  return generateId(`ds_${name}`);
};

// ==================== 对象操作工具 ====================

/**
 * 深度克隆对象
 */
export const deepClone = <T>(obj: T): T => {
  return cloneDeep(obj);
};

/**
 * 深度合并对象
 */
export const deepMerge = <T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T => {
  return merge(target, ...sources);
};

/**
 * 深度比较对象
 */
export const deepEqual = (obj1: any, obj2: any): boolean => {
  return isEqual(obj1, obj2);
};

/**
 * 获取对象属性值（支持路径）
 */
export const getProperty = (obj: any, path: string, defaultValue?: any): any => {
  if (!obj || !path) return defaultValue;
  
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result !== undefined ? result : defaultValue;
};

/**
 * 设置对象属性值（支持路径）
 */
export const setProperty = (obj: any, path: string, value: any): void => {
  if (!obj || !path) return;
  
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  let current = obj;
  
  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[lastKey] = value;
};

/**
 * 删除对象属性（支持路径）
 */
export const deleteProperty = (obj: any, path: string): boolean => {
  if (!obj || !path) return false;
  
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  let current = obj;
  
  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      return false;
    }
    current = current[key];
  }
  
  if (lastKey in current) {
    delete current[lastKey];
    return true;
  }
  
  return false;
};

// ==================== 数组操作工具 ====================

/**
 * 数组去重
 */
export const unique = <T>(arr: T[], key?: keyof T): T[] => {
  if (!key) {
    return [...new Set(arr)];
  }
  
  const seen = new Set();
  return arr.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
};

/**
 * 数组分组
 */
export const groupBy = <T>(arr: T[], key: keyof T | ((item: T) => string)): Record<string, T[]> => {
  const getKey = typeof key === 'function' ? key : (item: T) => String(item[key]);
  
  return arr.reduce((groups, item) => {
    const groupKey = getKey(item);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

/**
 * 数组排序
 */
export const sortBy = <T>(arr: T[], key: keyof T | ((item: T) => any), desc = false): T[] => {
  const getValue = typeof key === 'function' ? key : (item: T) => item[key];
  
  return [...arr].sort((a, b) => {
    const valueA = getValue(a);
    const valueB = getValue(b);
    
    if (valueA < valueB) return desc ? 1 : -1;
    if (valueA > valueB) return desc ? -1 : 1;
    return 0;
  });
};

// ==================== 字符串操作工具 ====================

/**
 * 驼峰命名转换
 */
export const camelCase = (str: string): string => {
  return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
};

/**
 * 短横线命名转换
 */
export const kebabCase = (str: string): string => {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
};

/**
 * 首字母大写
 */
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * 截断字符串
 */
export const truncate = (str: string, length: number, suffix = '...'): string => {
  if (str.length <= length) return str;
  return str.slice(0, length - suffix.length) + suffix;
};

// ==================== 类型检查工具 ====================

/**
 * 检查是否为空值
 */
export const isEmpty = (value: any): boolean => {
  if (value == null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * 检查是否为函数
 */
export const isFunction = (value: any): value is Function => {
  return typeof value === 'function';
};

/**
 * 检查是否为对象
 */
export const isObject = (value: any): value is Record<string, any> => {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
};

/**
 * 检查是否为Promise
 */
export const isPromise = (value: any): value is Promise<any> => {
  return value && typeof value.then === 'function';
};

// ==================== 防抖节流工具 ====================

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): T => {
  let timeout: NodeJS.Timeout | null = null;
  
  return ((...args: Parameters<T>) => {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(null, args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(null, args);
  }) as T;
};

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T => {
  let timeout: NodeJS.Timeout | null = null;
  let previous = 0;
  
  return ((...args: Parameters<T>) => {
    const now = Date.now();
    const remaining = wait - (now - previous);
    
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      func.apply(null, args);
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now();
        timeout = null;
        func.apply(null, args);
      }, remaining);
    }
  }) as T;
};

// ==================== 异步工具 ====================

/**
 * 延迟执行
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 重试执行
 */
export const retry = async <T>(
  fn: () => Promise<T>,
  maxAttempts = 3,
  delayMs = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (attempt < maxAttempts) {
        await delay(delayMs * attempt);
      }
    }
  }
  
  throw lastError!;
};

/**
 * 超时控制
 */
export const timeout = <T>(promise: Promise<T>, ms: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Timeout after ${ms}ms`)), ms);
    })
  ]);
};
