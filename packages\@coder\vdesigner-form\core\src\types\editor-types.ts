/**
 * 编辑器相关类型定义
 */

import type { Component } from 'vue';

// ==================== 编辑器基础类型 ====================

/**
 * 编辑器类型枚举
 */
export enum EditorType {
  /** 文本输入 */
  TEXT = 'text',
  /** 数字输入 */
  NUMBER = 'number',
  /** 布尔值 */
  BOOLEAN = 'boolean',
  /** 选择器 */
  SELECT = 'select',
  /** 多选 */
  CHECKBOX = 'checkbox',
  /** 单选 */
  RADIO = 'radio',
  /** 颜色选择器 */
  COLOR = 'color',
  /** 日期选择器 */
  DATE = 'date',
  /** 时间选择器 */
  TIME = 'time',
  /** 文件上传 */
  FILE = 'file',
  /** 图片上传 */
  IMAGE = 'image',
  /** 富文本编辑器 */
  RICH_TEXT = 'richText',
  /** 代码编辑器 */
  CODE = 'code',
  /** JSON编辑器 */
  JSON = 'json',
  /** 表达式编辑器 */
  EXPRESSION = 'expression',
  /** 数据源选择器 */
  DATASOURCE = 'dataSource',
  /** 字段映射 */
  FIELD_MAPPING = 'fieldMapping',
  /** 样式编辑器 */
  STYLE = 'style',
  /** 事件编辑器 */
  EVENT = 'event',
  /** 自定义编辑器 */
  CUSTOM = 'custom'
}

/**
 * 编辑器配置基类
 */
export interface BaseEditorConfig {
  /** 编辑器类型 */
  type: EditorType;
  /** 编辑器标题 */
  title?: string;
  /** 编辑器描述 */
  description?: string;
  /** 是否必填 */
  required?: boolean;
  /** 默认值 */
  defaultValue?: any;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 占位符 */
  placeholder?: string;
  /** 验证规则 */
  rules?: any[];
  /** 显示条件 */
  showWhen?: (value: any, allValues: Record<string, any>) => boolean;
}

// ==================== 具体编辑器配置类型 ====================

/**
 * 文本编辑器配置
 */
export interface TextEditorConfig extends BaseEditorConfig {
  type: EditorType.TEXT;
  /** 最大长度 */
  maxLength?: number;
  /** 最小长度 */
  minLength?: number;
  /** 是否多行 */
  multiline?: boolean;
  /** 行数 */
  rows?: number;
  /** 是否自动调整高度 */
  autoSize?: boolean;
}

/**
 * 数字编辑器配置
 */
export interface NumberEditorConfig extends BaseEditorConfig {
  type: EditorType.NUMBER;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 步长 */
  step?: number;
  /** 精度 */
  precision?: number;
  /** 数字格式化 */
  formatter?: (value: number) => string;
  /** 数字解析 */
  parser?: (value: string) => number;
}

/**
 * 选择器编辑器配置
 */
export interface SelectEditorConfig extends BaseEditorConfig {
  type: EditorType.SELECT;
  /** 选项列表 */
  options: Array<{ label: string; value: any; disabled?: boolean }>;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否可搜索 */
  showSearch?: boolean;
  /** 是否允许清除 */
  allowClear?: boolean;
  /** 选项过滤函数 */
  filterOption?: (input: string, option: any) => boolean;
}

/**
 * 颜色选择器配置
 */
export interface ColorEditorConfig extends BaseEditorConfig {
  type: EditorType.COLOR;
  /** 是否显示透明度 */
  showAlpha?: boolean;
  /** 预设颜色 */
  presetColors?: string[];
  /** 颜色格式 */
  format?: 'hex' | 'rgb' | 'hsl';
}

/**
 * 代码编辑器配置
 */
export interface CodeEditorConfig extends BaseEditorConfig {
  type: EditorType.CODE;
  /** 编程语言 */
  language?: string;
  /** 主题 */
  theme?: string;
  /** 是否显示行号 */
  lineNumbers?: boolean;
  /** 是否自动换行 */
  wordWrap?: boolean;
  /** 最小行数 */
  minLines?: number;
  /** 最大行数 */
  maxLines?: number;
}

/**
 * 数据源选择器配置
 */
export interface DataSourceEditorConfig extends BaseEditorConfig {
  type: EditorType.DATASOURCE;
  /** 允许的数据源类型 */
  allowedTypes?: string[];
  /** 是否允许创建新数据源 */
  allowCreate?: boolean;
  /** 是否显示参数配置 */
  showParams?: boolean;
}

/**
 * 样式编辑器配置
 */
export interface StyleEditorConfig extends BaseEditorConfig {
  type: EditorType.STYLE;
  /** 支持的样式属性 */
  properties?: string[];
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 预设样式 */
  presets?: Array<{ name: string; styles: Record<string, any> }>;
}

/**
 * 事件编辑器配置
 */
export interface EventEditorConfig extends BaseEditorConfig {
  type: EditorType.EVENT;
  /** 支持的事件类型 */
  eventTypes?: string[];
  /** 是否支持代码编辑 */
  allowCode?: boolean;
  /** 是否支持可视化编辑 */
  allowVisual?: boolean;
}

// ==================== 编辑器组件类型 ====================

/**
 * 编辑器组件属性
 */
export interface EditorComponentProps {
  /** 当前值 */
  value: any;
  /** 编辑器配置 */
  config: BaseEditorConfig;
  /** 所有表单值 */
  formValues?: Record<string, any>;
  /** 值变化回调 */
  onChange: (value: any) => void;
  /** 失焦回调 */
  onBlur?: () => void;
  /** 聚焦回调 */
  onFocus?: () => void;
}

/**
 * 编辑器组件定义
 */
export interface EditorComponentDefinition {
  /** 编辑器类型 */
  type: EditorType;
  /** 编辑器名称 */
  name: string;
  /** 编辑器组件 */
  component: Component;
  /** 默认配置 */
  defaultConfig?: Partial<BaseEditorConfig>;
  /** 配置验证器 */
  validateConfig?: (config: BaseEditorConfig) => boolean;
}

// ==================== 编辑器注册表类型 ====================

/**
 * 编辑器注册表接口
 */
export interface EditorRegistry {
  /** 注册编辑器 */
  register(definition: EditorComponentDefinition): void;
  /** 获取编辑器组件 */
  getEditor(type: EditorType): Component | undefined;
  /** 获取编辑器定义 */
  getDefinition(type: EditorType): EditorComponentDefinition | undefined;
  /** 获取所有编辑器类型 */
  getAllTypes(): EditorType[];
  /** 创建编辑器配置 */
  createConfig(type: EditorType, config?: Partial<BaseEditorConfig>): BaseEditorConfig;
}

// ==================== 编辑器构建器类型 ====================

/**
 * 编辑器构建器选项
 */
export interface EditorBuilderOptions {
  /** 目标对象 */
  target: any;
  /** 属性路径 */
  propertyPath: string;
  /** 编辑器配置 */
  editorConfig: BaseEditorConfig;
  /** 上下文数据 */
  context?: Record<string, any>;
}

/**
 * 编辑器构建器接口
 */
export interface EditorBuilder {
  /** 构建编辑器 */
  build(options: EditorBuilderOptions): Component;
  /** 获取属性值 */
  getValue(target: any, propertyPath: string): any;
  /** 设置属性值 */
  setValue(target: any, propertyPath: string, value: any): void;
  /** 验证属性值 */
  validate(target: any, propertyPath: string, config: BaseEditorConfig): boolean;
}

// ==================== 表单编辑器类型 ====================

/**
 * 表单字段定义
 */
export interface FormFieldDefinition {
  /** 字段名 */
  name: string;
  /** 字段标签 */
  label: string;
  /** 编辑器配置 */
  editor: BaseEditorConfig;
  /** 字段分组 */
  group?: string;
  /** 排序权重 */
  order?: number;
  /** 字段依赖 */
  dependencies?: string[];
}

/**
 * 表单分组定义
 */
export interface FormGroupDefinition {
  /** 分组名称 */
  name: string;
  /** 分组标题 */
  title: string;
  /** 分组描述 */
  description?: string;
  /** 是否可折叠 */
  collapsible?: boolean;
  /** 默认是否展开 */
  defaultExpanded?: boolean;
  /** 排序权重 */
  order?: number;
}

/**
 * 动态表单配置
 */
export interface DynamicFormConfig {
  /** 表单字段 */
  fields: FormFieldDefinition[];
  /** 表单分组 */
  groups?: FormGroupDefinition[];
  /** 表单布局 */
  layout?: 'horizontal' | 'vertical' | 'inline';
  /** 标签宽度 */
  labelWidth?: string;
  /** 是否显示重置按钮 */
  showReset?: boolean;
  /** 是否显示提交按钮 */
  showSubmit?: boolean;
}
