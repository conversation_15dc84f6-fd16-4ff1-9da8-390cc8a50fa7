/**
 * 设计器管理器
 * 管理多个设计器实例的生命周期
 */

import type { 
  DesignerPropsType, 
  RenderConfig 
} from '../../types';

import { 
  ObjectCollection,
  EventEmitter,
  Singleton,
  generateId
} from '@coder/vdesigner-shared';

import { DesignerCore, type DesignerConfig } from './designer-core';

// ==================== 设计器管理器 ====================

/**
 * 设计器管理器
 */
export class DesignerManager extends Singleton {
  /** 设计器集合 */
  private designers = new ObjectCollection<DesignerCore>();
  
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();
  
  /** 默认设计器配置 */
  private defaultConfig: DesignerConfig = {
    maxHistorySize: 50,
    autoSave: true,
    autoSaveInterval: 30000,
    gridSize: 8,
    snapToGrid: true
  };

  constructor() {
    super();
  }

  /**
   * 创建设计器
   */
  async createDesigner(
    designerId?: string,
    props?: DesignerPropsType,
    config?: DesignerConfig
  ): Promise<DesignerCore> {
    const id = designerId || generateId('designer');
    
    // 检查是否已存在
    if (this.designers.has(id)) {
      throw new Error(`Designer with id "${id}" already exists`);
    }
    
    // 创建设计器
    const designer = new DesignerCore(
      id,
      props?.renderConfig || {},
      {
        ...this.defaultConfig,
        ...config
      }
    );
    
    // 初始化设计器
    if (props) {
      await designer.initialize(props);
    }
    
    // 添加到集合
    this.designers.add(designer);
    
    // 监听设计器事件
    this.setupDesignerEventListeners(designer);
    
    // 发射创建事件
    this.eventEmitter.emit('designer:created' as any, {
      designerId: id,
      designer,
      timestamp: Date.now()
    });
    
    return designer;
  }

  /**
   * 获取设计器
   */
  getDesigner(designerId: string): DesignerCore | undefined {
    return this.designers.get(designerId);
  }

  /**
   * 移除设计器
   */
  removeDesigner(designerId: string): boolean {
    const designer = this.designers.get(designerId);
    if (designer) {
      // 销毁设计器
      designer.dispose();
      
      // 从集合中移除
      const removed = this.designers.remove(designerId);
      
      if (removed) {
        this.eventEmitter.emit('designer:removed' as any, {
          designerId,
          timestamp: Date.now()
        });
      }
      
      return removed;
    }
    return false;
  }

  /**
   * 获取所有设计器
   */
  getAllDesigners(): DesignerCore[] {
    return this.designers.getAll();
  }

  /**
   * 获取设计器数量
   */
  getDesignerCount(): number {
    return this.designers.size();
  }

  /**
   * 清空所有设计器
   */
  clearAllDesigners(): void {
    const designers = this.getAllDesigners();
    
    // 销毁所有设计器
    designers.forEach(designer => {
      designer.dispose();
    });
    
    // 清空集合
    this.designers.clear();
    
    this.eventEmitter.emit('designers:cleared' as any, {
      count: designers.length,
      timestamp: Date.now()
    });
  }

  /**
   * 更新默认配置
   */
  setDefaultConfig(config: Partial<DesignerConfig>): void {
    this.defaultConfig = {
      ...this.defaultConfig,
      ...config
    };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): DesignerConfig {
    return { ...this.defaultConfig };
  }

  /**
   * 监听设计器创建事件
   */
  onDesignerCreated(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('designer:created' as any, callback);
  }

  /**
   * 监听设计器移除事件
   */
  onDesignerRemoved(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('designer:removed' as any, callback);
  }

  /**
   * 监听设计器错误事件
   */
  onDesignerError(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('designer:error' as any, callback);
  }

  /**
   * 设置设计器事件监听器
   */
  private setupDesignerEventListeners(designer: DesignerCore): void {
    // 转发设计器事件到管理器
    const forwardEvent = (eventName: string) => {
      return (data: any) => {
        this.eventEmitter.emit(eventName as any, {
          ...data,
          managerId: this.id
        });
      };
    };

    // 监听各种设计器事件
    designer.eventEmitter.on('designer:initialized' as any, forwardEvent('designer:initialized'));
    designer.eventEmitter.on('designer:error' as any, forwardEvent('designer:error'));
    designer.eventEmitter.on('designer:widget:added' as any, forwardEvent('designer:widget:added'));
    designer.eventEmitter.on('designer:widget:removed' as any, forwardEvent('designer:widget:removed'));
    designer.eventEmitter.on('designer:widget:updated' as any, forwardEvent('designer:widget:updated'));
    designer.eventEmitter.on('designer:widget:moved' as any, forwardEvent('designer:widget:moved'));
    designer.eventEmitter.on('designer:widget:selected' as any, forwardEvent('designer:widget:selected'));
    designer.eventEmitter.on('designer:config:changed' as any, forwardEvent('designer:config:changed'));
    designer.eventEmitter.on('designer:auto:save' as any, forwardEvent('designer:auto:save'));
  }
}

// ==================== 设计器工厂 ====================

/**
 * 设计器工厂
 * 提供便捷的设计器创建方法
 */
export class DesignerFactory {
  private manager: DesignerManager;

  constructor(manager: DesignerManager) {
    this.manager = manager;
  }

  /**
   * 创建标准设计器
   */
  async createStandardDesigner(props: DesignerPropsType): Promise<DesignerCore> {
    return this.manager.createDesigner(props.renderId, props, {
      autoSave: true,
      maxHistorySize: 50
    });
  }

  /**
   * 创建只读设计器
   */
  async createReadonlyDesigner(props: DesignerPropsType): Promise<DesignerCore> {
    return this.manager.createDesigner(props.renderId, {
      ...props,
      readonly: true
    }, {
      autoSave: false,
      maxHistorySize: 0
    });
  }

  /**
   * 创建高级设计器
   */
  async createAdvancedDesigner(props: DesignerPropsType): Promise<DesignerCore> {
    return this.manager.createDesigner(props.renderId, props, {
      autoSave: true,
      autoSaveInterval: 10000, // 10秒
      maxHistorySize: 100,
      gridSize: 4,
      snapToGrid: true
    });
  }

  /**
   * 创建轻量级设计器
   */
  async createLightweightDesigner(props: DesignerPropsType): Promise<DesignerCore> {
    return this.manager.createDesigner(props.renderId, props, {
      autoSave: false,
      maxHistorySize: 10,
      gridSize: 8,
      snapToGrid: false
    });
  }
}

// ==================== 默认实例 ====================

/**
 * 默认设计器管理器实例
 */
export const defaultDesignerManager = DesignerManager.getInstance();

/**
 * 默认设计器工厂实例
 */
export const defaultDesignerFactory = new DesignerFactory(defaultDesignerManager);

// ==================== 便捷方法 ====================

/**
 * 创建设计器的便捷方法
 */
export const createDesigner = (props: DesignerPropsType, config?: DesignerConfig): Promise<DesignerCore> => {
  return defaultDesignerManager.createDesigner(props.renderId, props, config);
};

/**
 * 获取设计器的便捷方法
 */
export const getDesigner = (designerId: string): DesignerCore | undefined => {
  return defaultDesignerManager.getDesigner(designerId);
};

/**
 * 移除设计器的便捷方法
 */
export const removeDesigner = (designerId: string): boolean => {
  return defaultDesignerManager.removeDesigner(designerId);
};
