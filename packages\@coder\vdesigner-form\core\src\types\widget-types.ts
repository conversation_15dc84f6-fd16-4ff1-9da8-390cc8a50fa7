/**
 * Widget 相关类型定义
 */

import type { Component } from 'vue';
import type { BaseRule, DefaultEditoOptions, IDataSourceRef } from './index';

// ==================== Widget 基础类型 ====================

/**
 * Widget 属性类型基类
 */
export interface WidgetPropsType {
  /** 组件ID */
  id: string;
  /** 组件类型 */
  type: string;
  /** 是否设计模式 */
  isDesign?: boolean;
  /** 组件配置选项 */
  options: Record<string, any>;
  /** 表单数据 */
  formData?: Record<string, any>;
  /** 全局配置 */
  cfg?: Record<string, any>;
}

/**
 * Widget 实例接口
 */
export interface IWidget {
  /** 组件唯一标识 */
  id: string;
  /** 组件类型 */
  type: string;
  /** 组件选项 */
  options: DefaultEditoOptions & Record<string, any>;
  /** 子组件 */
  widgets: IWidget[];
  /** 验证规则 */
  rules?: BaseRule[];
  /** 数据源引用 */
  dataSourceRef: Record<string, IDataSourceRef>;
  /** 父组件引用 */
  parent?: IWidget;
  /** 克隆方法 */
  clone(): IWidget;
  /** 序列化方法 */
  toJSON(): string;
  /** 添加子组件 */
  addWidget(widget: IWidget, index?: number): void;
  /** 移除子组件 */
  removeWidget(widget: IWidget): void;
  /** 查找子组件 */
  findWidget(id: string): IWidget | undefined;
}

// ==================== 具体 Widget 类型 ====================

/**
 * 输入框组件选项
 */
export interface InputWidgetOptions extends DefaultEditoOptions {
  /** 占位符 */
  placeholder?: string;
  /** 最大长度 */
  maxLength?: number;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 输入框类型 */
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url';
  /** 前缀图标 */
  prefixIcon?: string;
  /** 后缀图标 */
  suffixIcon?: string;
  /** 是否显示清除按钮 */
  clearable?: boolean;
}

/**
 * 按钮组件选项
 */
export interface ButtonWidgetOptions extends DefaultEditoOptions {
  /** 按钮文本 */
  text?: string;
  /** 按钮类型 */
  type?: 'primary' | 'default' | 'dashed' | 'text' | 'link';
  /** 按钮大小 */
  size?: 'large' | 'middle' | 'small';
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否加载中 */
  loading?: boolean;
  /** 按钮形状 */
  shape?: 'default' | 'circle' | 'round';
  /** 图标 */
  icon?: string;
  /** 点击事件代码 */
  onClick?: string;
}

/**
 * 选择器组件选项
 */
export interface SelectWidgetOptions extends DefaultEditoOptions {
  /** 占位符 */
  placeholder?: string;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可搜索 */
  showSearch?: boolean;
  /** 是否允许清除 */
  allowClear?: boolean;
  /** 选项列表 */
  options?: Array<{ label: string; value: any; disabled?: boolean }>;
  /** 数据源名称 */
  dataSource?: string;
  /** 标签字段名 */
  labelField?: string;
  /** 值字段名 */
  valueField?: string;
}

/**
 * 日期选择器组件选项
 */
export interface DatePickerWidgetOptions extends DefaultEditoOptions {
  /** 占位符 */
  placeholder?: string;
  /** 日期格式 */
  format?: string;
  /** 值格式 */
  valueFormat?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示时间 */
  showTime?: boolean;
  /** 选择器类型 */
  picker?: 'date' | 'week' | 'month' | 'quarter' | 'year';
  /** 是否允许清除 */
  allowClear?: boolean;
}

/**
 * 容器组件选项
 */
export interface ContainerWidgetOptions extends DefaultEditoOptions {
  /** 容器标题 */
  title?: string;
  /** 是否显示边框 */
  bordered?: boolean;
  /** 内边距 */
  padding?: string;
  /** 背景色 */
  backgroundColor?: string;
  /** 最小高度 */
  minHeight?: string;
  /** 布局方向 */
  direction?: 'horizontal' | 'vertical';
  /** 对齐方式 */
  align?: 'start' | 'center' | 'end' | 'stretch';
  /** 间距 */
  gap?: string;
}

/**
 * 表单项组件选项
 */
export interface FormItemWidgetOptions extends DefaultEditoOptions {
  /** 标签文本 */
  label?: string;
  /** 字段名 */
  name?: string;
  /** 是否必填 */
  required?: boolean;
  /** 标签宽度 */
  labelWidth?: string;
  /** 标签对齐方式 */
  labelAlign?: 'left' | 'right';
  /** 帮助文本 */
  help?: string;
  /** 额外信息 */
  extra?: string;
  /** 是否显示冒号 */
  colon?: boolean;
}

// ==================== Widget 工厂类型 ====================

/**
 * Widget 创建器接口
 */
export interface WidgetCreator {
  /** 创建 Widget 实例 */
  create(type: string, options?: Record<string, any>): IWidget;
  /** 注册 Widget 类型 */
  register(type: string, creator: () => IWidget): void;
  /** 获取所有注册的类型 */
  getTypes(): string[];
}

/**
 * Widget 注册表接口
 */
export interface WidgetRegistry {
  /** 注册组件定义 */
  register(definition: any, component: Component, implement?: string): void;
  /** 创建组件实例 */
  createWidget(type: string, options?: Record<string, any>): IWidget;
  /** 获取组件定义 */
  getDefinition(type: string): any;
  /** 获取组件实现 */
  getComponent(type: string, implement?: string): Component;
  /** 获取所有组件定义 */
  getAllDefinitions(): any[];
}

// ==================== Widget 事件类型 ====================

/**
 * Widget 事件类型
 */
export type WidgetEventType = 
  | 'widget:created'
  | 'widget:updated'
  | 'widget:deleted'
  | 'widget:selected'
  | 'widget:moved'
  | 'widget:copied'
  | 'widget:pasted';

/**
 * Widget 事件数据
 */
export interface WidgetEventData {
  /** 事件类型 */
  type: WidgetEventType;
  /** 目标组件 */
  widget: IWidget;
  /** 额外数据 */
  data?: any;
  /** 时间戳 */
  timestamp: number;
}

// ==================== Widget 验证类型 ====================

/**
 * Widget 验证结果
 */
export interface WidgetValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * Widget 验证器接口
 */
export interface WidgetValidator {
  /** 验证组件 */
  validate(widget: IWidget): WidgetValidationResult;
  /** 验证组件树 */
  validateTree(rootWidget: IWidget): WidgetValidationResult;
}
