import type { Widget } from '../widgets';

import { getProperty, setProperty } from '@coder/vdesigner-shared';

import { useRenderStore } from '../render';

export const getValueFrom = (
  namePath: string,
  formData: Record<string, any>,
) => {
  return getProperty(formData, namePath);
};

export const setValueTo = (namePath: string, formData: any, value: any) => {
  setProperty(formData, namePath, value);
};

export const getValue = (
  widget: Widget,
  render: ReturnType<typeof useRenderStore>,
) => {
  if (widget.options.name === undefined) return;
  return getValueFrom(widget.options.name, render.formData);
};

export const setValue = (
  widget: Widget,
  value: any,
  render: ReturnType<typeof useRenderStore>,
) => {
  if (widget.options.name === undefined) return;
  return setValueTo(widget.options.name, render.formData, value);
};
