/**
 * 渲染引擎管理器
 * 管理多个渲染引擎实例的生命周期
 */

import type { 
  RenderPropsType, 
  RenderConfig,
  RenderModel
} from '../../types';

import { 
  ObjectCollection,
  EventEmitter,
  Singleton,
  generateId
} from '@coder/vdesigner-shared';

import { RenderEngine, type RenderOptions } from './render-engine';

// ==================== 渲染引擎管理器 ====================

/**
 * 渲染引擎管理器
 */
export class RenderEngineManager extends Singleton {
  /** 渲染引擎集合 */
  private engines = new ObjectCollection<RenderEngine>();
  
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();
  
  /** 默认渲染选项 */
  private defaultOptions: RenderOptions = {
    enableCache: true,
    cacheTime: 5 * 60 * 1000,
    enableVirtualScroll: false,
    enableLazyLoad: false,
    enablePerformanceMonitor: true
  };

  constructor() {
    super();
  }

  /**
   * 创建渲染引擎
   */
  async createEngine(
    renderId?: string, 
    props?: RenderPropsType,
    options?: RenderOptions
  ): Promise<RenderEngine> {
    const id = renderId || generateId('render');
    
    // 检查是否已存在
    if (this.engines.has(id)) {
      throw new Error(`Render engine with id "${id}" already exists`);
    }
    
    // 创建引擎
    const engine = new RenderEngine(id, {
      renderId: id,
      isDesign: props?.isDesign || false,
      implement: props?.implement || 'default',
      cfg: props?.cfg || {},
      formData: props?.formData || {}
    }, {
      ...this.defaultOptions,
      ...options
    });
    
    // 初始化引擎
    if (props) {
      await engine.initialize(props);
    }
    
    // 添加到集合
    this.engines.add(engine);
    
    // 监听引擎事件
    this.setupEngineEventListeners(engine);
    
    // 发射创建事件
    this.eventEmitter.emit('engine:created' as any, {
      renderId: id,
      engine,
      timestamp: Date.now()
    });
    
    return engine;
  }

  /**
   * 获取渲染引擎
   */
  getEngine(renderId: string): RenderEngine | undefined {
    return this.engines.get(renderId);
  }

  /**
   * 移除渲染引擎
   */
  removeEngine(renderId: string): boolean {
    const engine = this.engines.get(renderId);
    if (engine) {
      // 销毁引擎
      engine.dispose();
      
      // 从集合中移除
      const removed = this.engines.remove(renderId);
      
      if (removed) {
        this.eventEmitter.emit('engine:removed' as any, {
          renderId,
          timestamp: Date.now()
        });
      }
      
      return removed;
    }
    return false;
  }

  /**
   * 获取所有渲染引擎
   */
  getAllEngines(): RenderEngine[] {
    return this.engines.getAll();
  }

  /**
   * 获取引擎数量
   */
  getEngineCount(): number {
    return this.engines.size();
  }

  /**
   * 清空所有引擎
   */
  clearAllEngines(): void {
    const engines = this.getAllEngines();
    
    // 销毁所有引擎
    engines.forEach(engine => {
      engine.dispose();
    });
    
    // 清空集合
    this.engines.clear();
    
    this.eventEmitter.emit('engines:cleared' as any, {
      count: engines.length,
      timestamp: Date.now()
    });
  }

  /**
   * 更新默认选项
   */
  setDefaultOptions(options: Partial<RenderOptions>): void {
    this.defaultOptions = {
      ...this.defaultOptions,
      ...options
    };
  }

  /**
   * 获取默认选项
   */
  getDefaultOptions(): RenderOptions {
    return { ...this.defaultOptions };
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    totalEngines: number;
    totalRenderCount: number;
    averageRenderTime: number;
    cacheHitRate: number;
  } {
    const engines = this.getAllEngines();
    let totalRenderCount = 0;
    let totalRenderTime = 0;
    let totalCacheHits = 0;
    
    engines.forEach(engine => {
      const perfData = engine.getPerformanceData();
      totalRenderCount += perfData.renderCount;
      totalRenderTime += perfData.totalRenderTime;
      totalCacheHits += perfData.cacheHitRate;
    });
    
    return {
      totalEngines: engines.length,
      totalRenderCount,
      averageRenderTime: totalRenderCount > 0 ? totalRenderTime / totalRenderCount : 0,
      cacheHitRate: engines.length > 0 ? totalCacheHits / engines.length : 0
    };
  }

  /**
   * 监听引擎创建事件
   */
  onEngineCreated(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('engine:created' as any, callback);
  }

  /**
   * 监听引擎移除事件
   */
  onEngineRemoved(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('engine:removed' as any, callback);
  }

  /**
   * 监听引擎错误事件
   */
  onEngineError(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('engine:error' as any, callback);
  }

  /**
   * 设置引擎事件监听器
   */
  private setupEngineEventListeners(engine: RenderEngine): void {
    // 转发引擎事件到管理器
    const forwardEvent = (eventName: string) => {
      return (data: any) => {
        this.eventEmitter.emit(eventName as any, {
          ...data,
          managerId: this.id
        });
      };
    };

    // 监听各种引擎事件
    engine.eventEmitter.on('render:initialized' as any, forwardEvent('engine:initialized'));
    engine.eventEmitter.on('render:error' as any, forwardEvent('engine:error'));
    engine.eventEmitter.on('render:config:updated' as any, forwardEvent('engine:config:updated'));
    engine.eventEmitter.on('render:disposed' as any, forwardEvent('engine:disposed'));
  }
}

// ==================== 渲染工厂 ====================

/**
 * 渲染工厂
 * 提供便捷的渲染引擎创建方法
 */
export class RenderFactory {
  private manager: RenderEngineManager;

  constructor(manager: RenderEngineManager) {
    this.manager = manager;
  }

  /**
   * 创建标准渲染引擎
   */
  async createStandardEngine(props: RenderPropsType): Promise<RenderEngine> {
    return this.manager.createEngine(props.renderId, props, {
      enableCache: true,
      enablePerformanceMonitor: true
    });
  }

  /**
   * 创建高性能渲染引擎
   */
  async createHighPerformanceEngine(props: RenderPropsType): Promise<RenderEngine> {
    return this.manager.createEngine(props.renderId, props, {
      enableCache: true,
      enableVirtualScroll: true,
      enableLazyLoad: true,
      enablePerformanceMonitor: true
    });
  }

  /**
   * 创建设计器渲染引擎
   */
  async createDesignerEngine(props: RenderPropsType): Promise<RenderEngine> {
    return this.manager.createEngine(props.renderId, {
      ...props,
      isDesign: true
    }, {
      enableCache: false, // 设计器模式下禁用缓存
      enablePerformanceMonitor: true
    });
  }

  /**
   * 创建轻量级渲染引擎
   */
  async createLightweightEngine(props: RenderPropsType): Promise<RenderEngine> {
    return this.manager.createEngine(props.renderId, props, {
      enableCache: false,
      enableVirtualScroll: false,
      enableLazyLoad: false,
      enablePerformanceMonitor: false
    });
  }
}

// ==================== 默认实例 ====================

/**
 * 默认渲染引擎管理器实例
 */
export const defaultRenderManager = RenderEngineManager.getInstance();

/**
 * 默认渲染工厂实例
 */
export const defaultRenderFactory = new RenderFactory(defaultRenderManager);

// ==================== 便捷方法 ====================

/**
 * 创建渲染引擎的便捷方法
 */
export const createRenderEngine = (props: RenderPropsType, options?: RenderOptions): Promise<RenderEngine> => {
  return defaultRenderManager.createEngine(props.renderId, props, options);
};

/**
 * 获取渲染引擎的便捷方法
 */
export const getRenderEngine = (renderId: string): RenderEngine | undefined => {
  return defaultRenderManager.getEngine(renderId);
};

/**
 * 移除渲染引擎的便捷方法
 */
export const removeRenderEngine = (renderId: string): boolean => {
  return defaultRenderManager.removeEngine(renderId);
};
