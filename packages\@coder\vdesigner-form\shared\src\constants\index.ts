/**
 * 共享常量定义
 */

// ==================== 组件相关常量 ====================

/**
 * 默认组件前缀
 */
export const WIDGET_PREFIX = 'CoderVDesign';

/**
 * 组件类型常量
 */
export const WIDGET_TYPES = {
  // 基础组件
  PAGE: `${WIDGET_PREFIX}Page`,
  CONTAINER: `${WIDGET_PREFIX}Container`,
  FORM: `${WIDGET_PREFIX}Form`,
  FORM_ITEM: `${WIDGET_PREFIX}FormItem`,
  
  // 输入组件
  INPUT: `${WIDGET_PREFIX}Input`,
  TEXTAREA: `${WIDGET_PREFIX}Textarea`,
  NUMBER: `${WIDGET_PREFIX}Number`,
  PASSWORD: `${WIDGET_PREFIX}Password`,
  
  // 选择组件
  SELECT: `${WIDGET_PREFIX}Select`,
  RADIO: `${WIDGET_PREFIX}Radio`,
  CHECKBOX: `${WIDGET_PREFIX}Checkbox`,
  SWITCH: `${WIDGET_PREFIX}Switch`,
  
  // 日期时间组件
  DATE_PICKER: `${WIDGET_PREFIX}DatePicker`,
  TIME_PICKER: `${WIDGET_PREFIX}TimePicker`,
  DATE_RANGE_PICKER: `${WIDGET_PREFIX}DateRangePicker`,
  
  // 上传组件
  UPLOAD: `${WIDGET_PREFIX}Upload`,
  FILE_UPLOAD: `${WIDGET_PREFIX}FileUpload`,
  IMAGE_UPLOAD: `${WIDGET_PREFIX}ImageUpload`,
  
  // 显示组件
  TEXT: `${WIDGET_PREFIX}Text`,
  LABEL: `${WIDGET_PREFIX}Label`,
  DIVIDER: `${WIDGET_PREFIX}Divider`,
  IMAGE: `${WIDGET_PREFIX}Image`,
  
  // 操作组件
  BUTTON: `${WIDGET_PREFIX}Button`,
  LINK: `${WIDGET_PREFIX}Link`,
  
  // 布局组件
  ROW: `${WIDGET_PREFIX}Row`,
  COL: `${WIDGET_PREFIX}Col`,
  GRID: `${WIDGET_PREFIX}Grid`,
  FLEX: `${WIDGET_PREFIX}Flex`,
  
  // 数据展示组件
  TABLE: `${WIDGET_PREFIX}Table`,
  LIST: `${WIDGET_PREFIX}List`,
  TREE: `${WIDGET_PREFIX}Tree`,
  
  // 反馈组件
  ALERT: `${WIDGET_PREFIX}Alert`,
  MESSAGE: `${WIDGET_PREFIX}Message`,
  NOTIFICATION: `${WIDGET_PREFIX}Notification`,
  
  // 导航组件
  MENU: `${WIDGET_PREFIX}Menu`,
  BREADCRUMB: `${WIDGET_PREFIX}Breadcrumb`,
  TABS: `${WIDGET_PREFIX}Tabs`,
  
  // 其他组件
  MODAL: `${WIDGET_PREFIX}Modal`,
  DRAWER: `${WIDGET_PREFIX}Drawer`,
  POPOVER: `${WIDGET_PREFIX}Popover`,
  TOOLTIP: `${WIDGET_PREFIX}Tooltip`
} as const;

/**
 * 组件分组常量
 */
export const WIDGET_GROUPS = {
  BASIC: 'basic',
  LAYOUT: 'layout',
  FORM: 'form',
  DISPLAY: 'display',
  FEEDBACK: 'feedback',
  NAVIGATION: 'navigation',
  CUSTOMER: 'customer'
} as const;

/**
 * 组件模式常量
 */
export const WIDGET_MODES = {
  CONTAINER: 'Container',
  ITEM: 'Item',
  MULTI_CONTAINER: 'Multi-Container'
} as const;

// ==================== 数据源相关常量 ====================

/**
 * 数据源类型常量
 */
export const DATASOURCE_TYPES = {
  AJAX: 'ajax',
  STATIC: 'static',
  CODE: 'code',
  KEY_MAP: 'keyMap',
  WEBSOCKET: 'websocket',
  GRAPHQL: 'graphql',
  LOCAL_STORAGE: 'localStorage',
  SESSION_STORAGE: 'sessionStorage'
} as const;

/**
 * 数据源状态常量
 */
export const DATASOURCE_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  CANCELLED: 'cancelled'
} as const;

/**
 * HTTP 方法常量
 */
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
  HEAD: 'HEAD',
  OPTIONS: 'OPTIONS'
} as const;

// ==================== 编辑器相关常量 ====================

/**
 * 编辑器类型常量
 */
export const EDITOR_TYPES = {
  TEXT: 'text',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  SELECT: 'select',
  CHECKBOX: 'checkbox',
  RADIO: 'radio',
  COLOR: 'color',
  DATE: 'date',
  TIME: 'time',
  FILE: 'file',
  IMAGE: 'image',
  RICH_TEXT: 'richText',
  CODE: 'code',
  JSON: 'json',
  EXPRESSION: 'expression',
  DATASOURCE: 'dataSource',
  FIELD_MAPPING: 'fieldMapping',
  STYLE: 'style',
  EVENT: 'event',
  CUSTOM: 'custom'
} as const;

// ==================== 设备类型常量 ====================

/**
 * 设备类型常量
 */
export const DEVICE_TYPES = {
  MOBILE: 'mobile',
  PAD: 'pad',
  PC: 'pc'
} as const;

/**
 * 响应式断点常量
 */
export const BREAKPOINTS = {
  XS: 480,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1600
} as const;

// ==================== 事件相关常量 ====================

/**
 * Widget 事件类型常量
 */
export const WIDGET_EVENTS = {
  CREATED: 'widget:created',
  UPDATED: 'widget:updated',
  DELETED: 'widget:deleted',
  SELECTED: 'widget:selected',
  MOVED: 'widget:moved',
  COPIED: 'widget:copied',
  PASTED: 'widget:pasted'
} as const;

/**
 * 数据源事件类型常量
 */
export const DATASOURCE_EVENTS = {
  CREATED: 'datasource:created',
  UPDATED: 'datasource:updated',
  DELETED: 'datasource:deleted',
  EXECUTED: 'datasource:executed',
  ERROR: 'datasource:error',
  CACHED: 'datasource:cached'
} as const;

/**
 * 渲染器事件类型常量
 */
export const RENDER_EVENTS = {
  MOUNTED: 'render:mounted',
  UNMOUNTED: 'render:unmounted',
  UPDATED: 'render:updated',
  ERROR: 'render:error'
} as const;

/**
 * 设计器事件类型常量
 */
export const DESIGNER_EVENTS = {
  WIDGET_SELECTED: 'designer:widget:selected',
  WIDGET_ADDED: 'designer:widget:added',
  WIDGET_REMOVED: 'designer:widget:removed',
  WIDGET_MOVED: 'designer:widget:moved',
  WIDGET_COPIED: 'designer:widget:copied',
  WIDGET_PASTED: 'designer:widget:pasted',
  CONFIG_CHANGED: 'designer:config:changed',
  MODE_CHANGED: 'designer:mode:changed'
} as const;

// ==================== 配置相关常量 ====================

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
  // 渲染器默认配置
  RENDER: {
    IMPLEMENT: 'default',
    CACHE_TIME: 5 * 60 * 1000, // 5分钟
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000,
    TIMEOUT: 30 * 1000 // 30秒
  },
  
  // 设计器默认配置
  DESIGNER: {
    AUTO_SAVE: true,
    AUTO_SAVE_INTERVAL: 30 * 1000, // 30秒
    UNDO_LIMIT: 50,
    GRID_SIZE: 8,
    SNAP_TO_GRID: true
  },
  
  // 组件默认配置
  WIDGET: {
    LABEL_WIDTH: 80,
    LABEL_POSITION: 'left',
    SIZE: 'middle',
    SPAN: 12
  }
} as const;

// ==================== 错误码常量 ====================

/**
 * 错误码常量
 */
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  OPERATION_FAILED: 'OPERATION_FAILED',
  
  // 组件相关错误
  WIDGET_NOT_FOUND: 'WIDGET_NOT_FOUND',
  WIDGET_TYPE_NOT_SUPPORTED: 'WIDGET_TYPE_NOT_SUPPORTED',
  WIDGET_VALIDATION_FAILED: 'WIDGET_VALIDATION_FAILED',
  
  // 数据源相关错误
  DATASOURCE_NOT_FOUND: 'DATASOURCE_NOT_FOUND',
  DATASOURCE_EXECUTION_FAILED: 'DATASOURCE_EXECUTION_FAILED',
  DATASOURCE_TIMEOUT: 'DATASOURCE_TIMEOUT',
  
  // 渲染相关错误
  RENDER_FAILED: 'RENDER_FAILED',
  RENDER_CONFIG_INVALID: 'RENDER_CONFIG_INVALID',
  
  // 网络相关错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',
  REQUEST_CANCELLED: 'REQUEST_CANCELLED'
} as const;

// ==================== 存储键常量 ====================

/**
 * 本地存储键常量
 */
export const STORAGE_KEYS = {
  DESIGNER_CONFIG: 'vdesigner_designer_config',
  RENDER_CACHE: 'vdesigner_render_cache',
  USER_PREFERENCES: 'vdesigner_user_preferences',
  WIDGET_TEMPLATES: 'vdesigner_widget_templates',
  DATASOURCE_CACHE: 'vdesigner_datasource_cache'
} as const;
