{"name": "@coder/vdesigner-shared", "version": "0.0.0", "private": true, "type": "module", "description": "VDesigner Form 共享模块", "scripts": {"build": "vite build", "test": "jest"}, "main": "src/index.ts", "typings": "src/index.ts", "publishConfig": {"main": "dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs"}}, "typings": "dist/index.d.ts"}, "dependencies": {"@vueuse/core": "catalog:", "lodash-es": "catalog:coder", "mitt": "catalog:", "vue": "catalog:"}, "devDependencies": {"@jest/globals": "catalog:coder", "@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:^", "@vben/vite-config": "workspace:*", "jest": "catalog:coder", "ts-jest": "catalog:coder", "ts-node": "catalog:coder"}}