{"name": "@coder/vdesigner-form-designer", "version": "0.2.0", "description": "Visual form designer component for Vue 3 with drag-and-drop functionality", "keywords": ["vue", "vue3", "form-designer", "visual-designer", "drag-drop", "form-builder", "low-code"], "homepage": "https://github.com/coder/vdesigner-form-designer", "repository": {"type": "git", "url": "https://github.com/coder/vdesigner-form-designer.git", "directory": "packages/@coder/vdesigner-form/designer"}, "license": "MIT", "author": "Coder Team", "type": "module", "scripts": {"build": "vite build", "build:watch": "vite build --watch", "dev": "vite", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage"}, "files": ["CHANGELOG.md", "README.md", "dist", "lib"], "main": "lib/index.ts", "module": "lib/index.ts", "types": "lib/index.ts", "exports": {".": {"types": "./lib/index.ts", "import": "./lib/index.ts"}, "./styles": {"import": "./lib/designer/designer.scss"}}, "publishConfig": {"access": "public", "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}, "./styles": {"import": "./dist/vdesigner-form-designer.css"}}}, "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/code-editor": "workspace:*", "@coder/http-request-setting": "workspace:*", "@coder/monaco-editor-builder": "workspace:^", "@coder/object-editor": "workspace:*", "@coder/rich-editor": "workspace:", "@coder/string-format": "workspace:*", "@coder/vdesigner-core": "workspace:^", "@coder/vdesigner-form-antdv": "workspace:*", "@coder/vdesigner-form-render": "workspace:^", "@coder/vdesigner-plugins-axios": "workspace:^", "@coder/vdesigner-plugins-router": "workspace:^", "@vben-core/icons": "workspace:*", "@vben-core/shadcn-ui": "workspace:^", "@vben/icons": "workspace:^", "@vben/request": "workspace:^", "@vben/styles": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "axios": "catalog:", "dom-align": "catalog:coder", "lodash-es": "catalog:coder", "mitt": "catalog:", "monaco-editor": "catalog:", "openapi-typescript": "catalog:", "pinia": "catalog:", "splitpanes": "catalog:coder", "vue": "catalog:", "vue-draggable-plus": "catalog:coder", "vue3-colorpicker": "catalog:coder"}, "devDependencies": {"@coder/system-api": "workspace:*", "@coder/vdesigner-form-vant": "workspace:^", "@coder/vdesigner-widget-http-file": "workspace:^", "@types/lodash-es": "catalog:coder", "@types/splitpanes": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*", "@vitejs/plugin-vue": "catalog:", "mockjs": "catalog:", "vite-plugin-mock": "catalog:coder"}}