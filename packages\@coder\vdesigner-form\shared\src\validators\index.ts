/**
 * 验证器系统
 * 提供通用的数据验证功能
 */

// ==================== 验证结果接口 ====================

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否验证通过 */
  valid: boolean;
  /** 错误信息列表 */
  errors: string[];
  /** 警告信息列表 */
  warnings?: string[];
  /** 验证的字段路径 */
  field?: string;
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  /** 规则名称 */
  name: string;
  /** 验证函数 */
  validator: (value: any, params?: any) => boolean | string;
  /** 错误消息模板 */
  message?: string;
  /** 规则参数 */
  params?: any;
}

/**
 * 字段验证配置
 */
export interface FieldValidationConfig {
  /** 字段名称 */
  field: string;
  /** 字段标签 */
  label?: string;
  /** 验证规则列表 */
  rules: ValidationRule[];
  /** 是否必填 */
  required?: boolean;
  /** 自定义错误消息 */
  customMessages?: Record<string, string>;
}

// ==================== 内置验证规则 ====================

/**
 * 内置验证规则
 */
export const builtInRules: Record<string, ValidationRule> = {
  // 必填验证
  required: {
    name: 'required',
    validator: (value: any) => {
      if (value === null || value === undefined) return false;
      if (typeof value === 'string') return value.trim() !== '';
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'object') return Object.keys(value).length > 0;
      return true;
    },
    message: '此字段为必填项'
  },

  // 字符串长度验证
  minLength: {
    name: 'minLength',
    validator: (value: any, min: number) => {
      if (!value) return true; // 空值跳过验证
      return String(value).length >= min;
    },
    message: '长度不能少于 {min} 个字符'
  },

  maxLength: {
    name: 'maxLength',
    validator: (value: any, max: number) => {
      if (!value) return true;
      return String(value).length <= max;
    },
    message: '长度不能超过 {max} 个字符'
  },

  // 数值范围验证
  min: {
    name: 'min',
    validator: (value: any, min: number) => {
      if (value === null || value === undefined || value === '') return true;
      const num = Number(value);
      return !isNaN(num) && num >= min;
    },
    message: '值不能小于 {min}'
  },

  max: {
    name: 'max',
    validator: (value: any, max: number) => {
      if (value === null || value === undefined || value === '') return true;
      const num = Number(value);
      return !isNaN(num) && num <= max;
    },
    message: '值不能大于 {max}'
  },

  // 正则表达式验证
  pattern: {
    name: 'pattern',
    validator: (value: any, pattern: RegExp | string) => {
      if (!value) return true;
      const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
      return regex.test(String(value));
    },
    message: '格式不正确'
  },

  // 邮箱验证
  email: {
    name: 'email',
    validator: (value: any) => {
      if (!value) return true;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(String(value));
    },
    message: '请输入有效的邮箱地址'
  },

  // 手机号验证
  phone: {
    name: 'phone',
    validator: (value: any) => {
      if (!value) return true;
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(String(value));
    },
    message: '请输入有效的手机号码'
  },

  // URL验证
  url: {
    name: 'url',
    validator: (value: any) => {
      if (!value) return true;
      try {
        new URL(String(value));
        return true;
      } catch {
        return false;
      }
    },
    message: '请输入有效的URL地址'
  },

  // 数字验证
  number: {
    name: 'number',
    validator: (value: any) => {
      if (value === null || value === undefined || value === '') return true;
      return !isNaN(Number(value));
    },
    message: '请输入有效的数字'
  },

  // 整数验证
  integer: {
    name: 'integer',
    validator: (value: any) => {
      if (value === null || value === undefined || value === '') return true;
      const num = Number(value);
      return !isNaN(num) && Number.isInteger(num);
    },
    message: '请输入整数'
  },

  // 数组长度验证
  arrayMinLength: {
    name: 'arrayMinLength',
    validator: (value: any, min: number) => {
      if (!Array.isArray(value)) return true;
      return value.length >= min;
    },
    message: '至少选择 {min} 项'
  },

  arrayMaxLength: {
    name: 'arrayMaxLength',
    validator: (value: any, max: number) => {
      if (!Array.isArray(value)) return true;
      return value.length <= max;
    },
    message: '最多选择 {max} 项'
  },

  // 自定义函数验证
  custom: {
    name: 'custom',
    validator: (value: any, fn: (value: any) => boolean | string) => {
      return fn(value);
    },
    message: '验证失败'
  }
};

// ==================== 验证器类 ====================

/**
 * 验证器类
 */
export class Validator {
  private rules = new Map<string, ValidationRule>();

  constructor() {
    // 注册内置规则
    Object.values(builtInRules).forEach(rule => {
      this.addRule(rule);
    });
  }

  /**
   * 添加验证规则
   */
  addRule(rule: ValidationRule): void {
    this.rules.set(rule.name, rule);
  }

  /**
   * 移除验证规则
   */
  removeRule(name: string): boolean {
    return this.rules.delete(name);
  }

  /**
   * 获取验证规则
   */
  getRule(name: string): ValidationRule | undefined {
    return this.rules.get(name);
  }

  /**
   * 验证单个值
   */
  validateValue(value: any, rules: ValidationRule[]): ValidationResult {
    const errors: string[] = [];

    for (const rule of rules) {
      const result = rule.validator(value, rule.params);
      
      if (result === false) {
        errors.push(this.formatMessage(rule.message || '验证失败', rule.params));
      } else if (typeof result === 'string') {
        errors.push(result);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证字段
   */
  validateField(value: any, config: FieldValidationConfig): ValidationResult {
    const errors: string[] = [];
    const field = config.field;

    // 必填验证
    if (config.required) {
      const requiredRule = this.rules.get('required');
      if (requiredRule && !requiredRule.validator(value)) {
        const message = config.customMessages?.required || requiredRule.message || '此字段为必填项';
        errors.push(this.formatMessage(message, { field: config.label || field }));
        
        // 如果必填验证失败，跳过其他验证
        return {
          valid: false,
          errors,
          field
        };
      }
    }

    // 其他规则验证
    for (const rule of config.rules) {
      const registeredRule = this.rules.get(rule.name);
      if (!registeredRule) {
        console.warn(`Unknown validation rule: ${rule.name}`);
        continue;
      }

      const result = registeredRule.validator(value, rule.params);
      
      if (result === false) {
        const message = config.customMessages?.[rule.name] || rule.message || registeredRule.message || '验证失败';
        errors.push(this.formatMessage(message, { 
          field: config.label || field,
          ...rule.params 
        }));
      } else if (typeof result === 'string') {
        errors.push(result);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      field
    };
  }

  /**
   * 验证对象
   */
  validateObject(data: Record<string, any>, configs: FieldValidationConfig[]): ValidationResult {
    const allErrors: string[] = [];
    let allValid = true;

    for (const config of configs) {
      const value = this.getNestedValue(data, config.field);
      const result = this.validateField(value, config);
      
      if (!result.valid) {
        allValid = false;
        allErrors.push(...result.errors);
      }
    }

    return {
      valid: allValid,
      errors: allErrors
    };
  }

  /**
   * 格式化错误消息
   */
  private formatMessage(template: string, params: Record<string, any> = {}): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }
}

// ==================== 表单验证器 ====================

/**
 * 表单验证器
 */
export class FormValidator extends Validator {
  private fieldConfigs = new Map<string, FieldValidationConfig>();

  /**
   * 添加字段配置
   */
  addField(config: FieldValidationConfig): void {
    this.fieldConfigs.set(config.field, config);
  }

  /**
   * 移除字段配置
   */
  removeField(field: string): boolean {
    return this.fieldConfigs.delete(field);
  }

  /**
   * 验证表单
   */
  validate(data: Record<string, any>): ValidationResult {
    const configs = Array.from(this.fieldConfigs.values());
    return this.validateObject(data, configs);
  }

  /**
   * 验证单个字段
   */
  validateSingleField(field: string, value: any): ValidationResult {
    const config = this.fieldConfigs.get(field);
    if (!config) {
      return { valid: true, errors: [] };
    }

    return this.validateField(value, config);
  }

  /**
   * 获取所有字段配置
   */
  getFieldConfigs(): FieldValidationConfig[] {
    return Array.from(this.fieldConfigs.values());
  }
}

// ==================== 工具函数 ====================

/**
 * 创建验证规则
 */
export const createRule = (
  name: string,
  validator: (value: any, params?: any) => boolean | string,
  message?: string,
  params?: any
): ValidationRule => {
  return { name, validator, message, params };
};

/**
 * 创建字段配置
 */
export const createFieldConfig = (
  field: string,
  rules: ValidationRule[],
  options: {
    label?: string;
    required?: boolean;
    customMessages?: Record<string, string>;
  } = {}
): FieldValidationConfig => {
  return {
    field,
    rules,
    label: options.label,
    required: options.required,
    customMessages: options.customMessages
  };
};

// ==================== 默认实例 ====================

/**
 * 默认验证器实例
 */
export const defaultValidator = new Validator();

/**
 * 默认表单验证器实例
 */
export const defaultFormValidator = new FormValidator();
