import type { RenderPropsType } from './RenderTypes';

import { createDataSourceDataStore, createRenderUseStore } from '../store';
import { generateId } from '../utils';
import { initRootWidget } from './render';

export * from '../store';
export * from './render';
export * from './renderMitter';
export * from './RenderTypes';
export * from './useEmitter';
export * from './engine';

const _renderStoreMap = new Map<
  string,
  ReturnType<ReturnType<typeof createRenderUseStore>>
>();

const _dataSourceStoreMap = new Map<
  string,
  ReturnType<ReturnType<typeof createDataSourceDataStore>>
>();
/**
 * 获取一个方法，获取DataSource属性。
 * @param name dataSource's name
 */
export const useRenderStore = (
  renderId: string,
  props?: RenderPropsType,
): ReturnType<ReturnType<typeof createRenderUseStore>> => {
  const store = _renderStoreMap.get(renderId);
  if (store) return store;

  const useStore = createRenderUseStore(renderId, props);
  const created = useStore();
  _renderStoreMap.set(renderId, created);
  return created;
};

export const useRenderDataSourceStore = (
  renderId: string,
): ReturnType<ReturnType<typeof createDataSourceDataStore>> => {
  const dataSourceId = `${renderId}/dataSource`;
  const store = _dataSourceStoreMap.get(dataSourceId);
  if (store) return store;

  const useStore = createDataSourceDataStore(renderId);
  const created = useStore();

  _dataSourceStoreMap.set(dataSourceId, created);
  return created;
};

export const useRender = (
  props: RenderPropsType,
  renderBuilder?: (renderId: string) => void,
) => {
  const renderId = props.renderId ?? generateId();

  renderBuilder && renderBuilder(renderId);

  if (props.renderConfig) initRootWidget(props);

  const renderStore = useRenderStore(renderId, props);

  if (!renderStore) throw new Error('render is undefined');

  const dataSourceStore = useRenderDataSourceStore(renderId);

  return {
    initDataSource: () => {
      if (!renderStore.dataSource) return;
      renderStore.dataSource.forEach((ds) => {
        dataSourceStore.invoke(ds, true);
      });
    },
    renderId,
  };
};
