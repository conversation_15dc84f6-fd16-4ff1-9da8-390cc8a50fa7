/**
 * 数据源相关类型定义
 */

// ==================== 数据源基础类型 ====================

/**
 * 数据源类型枚举
 */
export enum DataSourceType {
  /** Ajax 请求 */
  AJAX = 'ajax',
  /** 静态数据 */
  STATIC = 'static',
  /** 代码执行 */
  CODE = 'code',
  /** 键值映射 */
  KEY_MAP = 'keyMap',
  /** WebSocket */
  WEBSOCKET = 'websocket',
  /** GraphQL */
  GRAPHQL = 'graphql',
  /** 本地存储 */
  LOCAL_STORAGE = 'localStorage',
  /** 会话存储 */
  SESSION_STORAGE = 'sessionStorage'
}

/**
 * HTTP 请求方法
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

/**
 * 数据源状态
 */
export enum DataSourceStatus {
  /** 未初始化 */
  IDLE = 'idle',
  /** 加载中 */
  LOADING = 'loading',
  /** 成功 */
  SUCCESS = 'success',
  /** 失败 */
  ERROR = 'error',
  /** 已取消 */
  CANCELLED = 'cancelled'
}

// ==================== 数据源配置类型 ====================

/**
 * 数据源基础配置
 */
export interface BaseDataSourceConfig {
  /** 数据源类型 */
  type: DataSourceType;
  /** 数据源名称 */
  name: string;
  /** 数据源描述 */
  description?: string;
  /** 是否自动执行 */
  autoExecute?: boolean;
  /** 缓存时间（毫秒） */
  cacheTime?: number;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 重试次数 */
  retryCount?: number;
  /** 重试间隔（毫秒） */
  retryDelay?: number;
  /** 超时时间（毫秒） */
  timeout?: number;
}

/**
 * Ajax 数据源配置
 */
export interface AjaxDataSourceConfig extends BaseDataSourceConfig {
  type: DataSourceType.AJAX;
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method: HttpMethod;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求参数 */
  params?: Record<string, any>;
  /** 请求体 */
  data?: any;
  /** 响应数据路径 */
  dataPath?: string;
  /** 请求前处理函数 */
  beforeRequest?: string;
  /** 响应后处理函数 */
  afterResponse?: string;
  /** 错误处理函数 */
  onError?: string;
}

/**
 * 静态数据源配置
 */
export interface StaticDataSourceConfig extends BaseDataSourceConfig {
  type: DataSourceType.STATIC;
  /** 静态数据 */
  data: any;
  /** 数据转换函数 */
  transform?: string;
}

/**
 * 代码数据源配置
 */
export interface CodeDataSourceConfig extends BaseDataSourceConfig {
  type: DataSourceType.CODE;
  /** 执行代码 */
  code: string;
  /** 代码语言 */
  language?: 'javascript' | 'typescript';
  /** 是否异步执行 */
  async?: boolean;
  /** 上下文变量 */
  context?: Record<string, any>;
}

/**
 * 键值映射数据源配置
 */
export interface KeyMapDataSourceConfig extends BaseDataSourceConfig {
  type: DataSourceType.KEY_MAP;
  /** 键值映射表 */
  mapping: Record<string, any>;
  /** 默认值 */
  defaultValue?: any;
  /** 是否严格模式 */
  strict?: boolean;
}

/**
 * WebSocket 数据源配置
 */
export interface WebSocketDataSourceConfig extends BaseDataSourceConfig {
  type: DataSourceType.WEBSOCKET;
  /** WebSocket URL */
  url: string;
  /** 连接协议 */
  protocols?: string | string[];
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 连接事件处理 */
  onOpen?: string;
  /** 消息事件处理 */
  onMessage?: string;
  /** 关闭事件处理 */
  onClose?: string;
  /** 错误事件处理 */
  onError?: string;
}

// ==================== 数据源实例类型 ====================

/**
 * 数据源执行参数
 */
export interface DataSourceExecuteParams {
  /** 执行参数 */
  params?: Record<string, any>;
  /** 是否强制刷新 */
  forceRefresh?: boolean;
  /** 执行上下文 */
  context?: Record<string, any>;
  /** 取消令牌 */
  cancelToken?: AbortSignal;
}

/**
 * 数据源执行结果
 */
export interface DataSourceExecuteResult<T = any> {
  /** 执行状态 */
  status: DataSourceStatus;
  /** 返回数据 */
  data?: T;
  /** 错误信息 */
  error?: Error;
  /** 执行时间（毫秒） */
  duration?: number;
  /** 是否来自缓存 */
  fromCache?: boolean;
  /** 执行时间戳 */
  timestamp: number;
}

/**
 * 数据源实例接口
 */
export interface IDataSource<T = any> {
  /** 数据源ID */
  id: string;
  /** 数据源名称 */
  name: string;
  /** 数据源配置 */
  config: BaseDataSourceConfig;
  /** 当前状态 */
  status: DataSourceStatus;
  /** 当前数据 */
  data?: T;
  /** 最后错误 */
  error?: Error;
  /** 是否正在执行 */
  loading: boolean;
  
  /** 执行数据源 */
  execute(params?: DataSourceExecuteParams): Promise<DataSourceExecuteResult<T>>;
  /** 取消执行 */
  cancel(): void;
  /** 清除缓存 */
  clearCache(): void;
  /** 重置状态 */
  reset(): void;
  /** 销毁数据源 */
  destroy(): void;
  
  /** 监听状态变化 */
  onStatusChange(callback: (status: DataSourceStatus) => void): () => void;
  /** 监听数据变化 */
  onDataChange(callback: (data: T) => void): () => void;
  /** 监听错误 */
  onError(callback: (error: Error) => void): () => void;
}

// ==================== 数据源提供者类型 ====================

/**
 * 数据源提供者接口
 */
export interface IDataSourceProvider {
  /** 提供者类型 */
  type: DataSourceType;
  /** 提供者名称 */
  name: string;
  /** 创建数据源实例 */
  create(config: BaseDataSourceConfig): IDataSource;
  /** 验证配置 */
  validateConfig(config: BaseDataSourceConfig): boolean;
  /** 获取默认配置 */
  getDefaultConfig(): Partial<BaseDataSourceConfig>;
}

/**
 * 数据源管理器接口
 */
export interface IDataSourceManager {
  /** 注册数据源提供者 */
  registerProvider(provider: IDataSourceProvider): void;
  /** 创建数据源 */
  createDataSource(config: BaseDataSourceConfig): IDataSource;
  /** 获取数据源 */
  getDataSource(id: string): IDataSource | undefined;
  /** 移除数据源 */
  removeDataSource(id: string): void;
  /** 获取所有数据源 */
  getAllDataSources(): IDataSource[];
  /** 执行数据源 */
  executeDataSource(id: string, params?: DataSourceExecuteParams): Promise<DataSourceExecuteResult>;
  /** 批量执行数据源 */
  executeBatch(ids: string[], params?: DataSourceExecuteParams): Promise<DataSourceExecuteResult[]>;
  /** 清除所有缓存 */
  clearAllCache(): void;
  /** 销毁管理器 */
  destroy(): void;
}

// ==================== 数据源事件类型 ====================

/**
 * 数据源事件类型
 */
export type DataSourceEventType = 
  | 'datasource:created'
  | 'datasource:updated'
  | 'datasource:deleted'
  | 'datasource:executed'
  | 'datasource:error'
  | 'datasource:cached';

/**
 * 数据源事件数据
 */
export interface DataSourceEventData {
  /** 事件类型 */
  type: DataSourceEventType;
  /** 数据源ID */
  dataSourceId: string;
  /** 事件数据 */
  data?: any;
  /** 时间戳 */
  timestamp: number;
}

// ==================== 数据源工具类型 ====================

/**
 * 数据转换器接口
 */
export interface IDataTransformer {
  /** 转换数据 */
  transform(data: any, config?: Record<string, any>): any;
  /** 验证数据 */
  validate(data: any): boolean;
}

/**
 * 数据缓存接口
 */
export interface IDataCache {
  /** 获取缓存 */
  get(key: string): any;
  /** 设置缓存 */
  set(key: string, value: any, ttl?: number): void;
  /** 删除缓存 */
  delete(key: string): void;
  /** 清空缓存 */
  clear(): void;
  /** 检查是否存在 */
  has(key: string): boolean;
}

/**
 * 数据验证器接口
 */
export interface IDataValidator {
  /** 验证数据 */
  validate(data: any, schema?: any): { valid: boolean; errors: string[] };
  /** 添加验证规则 */
  addRule(name: string, rule: (value: any) => boolean): void;
  /** 移除验证规则 */
  removeRule(name: string): void;
}
