/**
 * 缓存系统
 * 提供内存缓存、本地存储缓存等功能
 */

import { EventEmitter } from '../events';

// ==================== 缓存接口定义 ====================

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  /** 缓存键 */
  key: string;
  /** 缓存值 */
  value: T;
  /** 过期时间戳 */
  expireAt?: number;
  /** 创建时间戳 */
  createdAt: number;
  /** 访问次数 */
  accessCount: number;
  /** 最后访问时间 */
  lastAccessAt: number;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** 最大缓存项数量 */
  maxSize?: number;
  /** 默认过期时间（毫秒） */
  defaultTTL?: number;
  /** 是否启用LRU淘汰策略 */
  enableLRU?: boolean;
  /** 清理间隔（毫秒） */
  cleanupInterval?: number;
}

/**
 * 缓存接口
 */
export interface ICache<T = any> {
  /** 获取缓存 */
  get(key: string): T | undefined;
  /** 设置缓存 */
  set(key: string, value: T, ttl?: number): void;
  /** 删除缓存 */
  delete(key: string): boolean;
  /** 检查是否存在 */
  has(key: string): boolean;
  /** 清空缓存 */
  clear(): void;
  /** 获取缓存大小 */
  size(): number;
  /** 获取所有键 */
  keys(): string[];
  /** 获取缓存统计信息 */
  getStats(): CacheStats;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 总请求次数 */
  totalRequests: number;
  /** 命中次数 */
  hits: number;
  /** 未命中次数 */
  misses: number;
  /** 命中率 */
  hitRate: number;
  /** 当前缓存项数量 */
  size: number;
  /** 最大缓存项数量 */
  maxSize: number;
}

// ==================== 内存缓存实现 ====================

/**
 * 内存缓存实现
 */
export class MemoryCache<T = any> implements ICache<T> {
  private items = new Map<string, CacheItem<T>>();
  private config: Required<CacheConfig>;
  private stats: CacheStats;
  private eventEmitter = new EventEmitter();
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: CacheConfig = {}) {
    this.config = {
      maxSize: config.maxSize || 1000,
      defaultTTL: config.defaultTTL || 5 * 60 * 1000, // 5分钟
      enableLRU: config.enableLRU !== false,
      cleanupInterval: config.cleanupInterval || 60 * 1000 // 1分钟
    };

    this.stats = {
      totalRequests: 0,
      hits: 0,
      misses: 0,
      hitRate: 0,
      size: 0,
      maxSize: this.config.maxSize
    };

    this.startCleanup();
  }

  get(key: string): T | undefined {
    this.stats.totalRequests++;

    const item = this.items.get(key);
    if (!item) {
      this.stats.misses++;
      this.updateHitRate();
      return undefined;
    }

    // 检查是否过期
    if (item.expireAt && Date.now() > item.expireAt) {
      this.items.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      this.updateSize();
      return undefined;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccessAt = Date.now();

    this.stats.hits++;
    this.updateHitRate();

    return item.value;
  }

  set(key: string, value: T, ttl?: number): void {
    const now = Date.now();
    const expireAt = ttl ? now + ttl : (this.config.defaultTTL ? now + this.config.defaultTTL : undefined);

    const item: CacheItem<T> = {
      key,
      value,
      expireAt,
      createdAt: now,
      accessCount: 0,
      lastAccessAt: now
    };

    // 如果已存在，直接更新
    if (this.items.has(key)) {
      this.items.set(key, item);
      return;
    }

    // 检查是否需要淘汰
    if (this.items.size >= this.config.maxSize) {
      this.evict();
    }

    this.items.set(key, item);
    this.updateSize();

    this.eventEmitter.emit('cache:set' as any, { key, value });
  }

  delete(key: string): boolean {
    const deleted = this.items.delete(key);
    if (deleted) {
      this.updateSize();
      this.eventEmitter.emit('cache:delete' as any, { key });
    }
    return deleted;
  }

  has(key: string): boolean {
    const item = this.items.get(key);
    if (!item) return false;

    // 检查是否过期
    if (item.expireAt && Date.now() > item.expireAt) {
      this.items.delete(key);
      this.updateSize();
      return false;
    }

    return true;
  }

  clear(): void {
    this.items.clear();
    this.updateSize();
    this.eventEmitter.emit('cache:clear' as any, {});
  }

  size(): number {
    return this.items.size;
  }

  keys(): string[] {
    return Array.from(this.items.keys());
  }

  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 淘汰策略
   */
  private evict(): void {
    if (!this.config.enableLRU) {
      // 简单的FIFO策略
      const firstKey = this.items.keys().next().value;
      if (firstKey) {
        this.items.delete(firstKey);
      }
      return;
    }

    // LRU策略：删除最久未访问的项
    let oldestKey: string | undefined;
    let oldestTime = Date.now();

    for (const [key, item] of this.items) {
      if (item.lastAccessAt < oldestTime) {
        oldestTime = item.lastAccessAt;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.items.delete(oldestKey);
    }
  }

  /**
   * 清理过期项
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, item] of this.items) {
      if (item.expireAt && now > item.expireAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.items.delete(key);
    });

    if (expiredKeys.length > 0) {
      this.updateSize();
      this.eventEmitter.emit('cache:cleanup' as any, { expiredKeys });
    }
  }

  /**
   * 开始定时清理
   */
  private startCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 停止定时清理
   */
  private stopCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? this.stats.hits / this.stats.totalRequests 
      : 0;
  }

  /**
   * 更新大小统计
   */
  private updateSize(): void {
    this.stats.size = this.items.size;
  }

  /**
   * 销毁缓存
   */
  destroy(): void {
    this.stopCleanup();
    this.clear();
    this.eventEmitter.clear();
  }
}

// ==================== 本地存储缓存实现 ====================

/**
 * 本地存储缓存实现
 */
export class LocalStorageCache<T = any> implements ICache<T> {
  private prefix: string;
  private defaultTTL: number;

  constructor(prefix = 'vdesigner_cache_', defaultTTL = 5 * 60 * 1000) {
    this.prefix = prefix;
    this.defaultTTL = defaultTTL;
  }

  get(key: string): T | undefined {
    try {
      const item = localStorage.getItem(this.getKey(key));
      if (!item) return undefined;

      const parsed = JSON.parse(item);
      
      // 检查是否过期
      if (parsed.expireAt && Date.now() > parsed.expireAt) {
        this.delete(key);
        return undefined;
      }

      return parsed.value;
    } catch {
      return undefined;
    }
  }

  set(key: string, value: T, ttl?: number): void {
    try {
      const expireAt = ttl ? Date.now() + ttl : (this.defaultTTL ? Date.now() + this.defaultTTL : undefined);
      const item = {
        value,
        expireAt,
        createdAt: Date.now()
      };

      localStorage.setItem(this.getKey(key), JSON.stringify(item));
    } catch {
      // 存储失败，可能是空间不足
    }
  }

  delete(key: string): boolean {
    try {
      localStorage.removeItem(this.getKey(key));
      return true;
    } catch {
      return false;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== undefined;
  }

  clear(): void {
    const keys = this.keys();
    keys.forEach(key => {
      localStorage.removeItem(this.getKey(key));
    });
  }

  size(): number {
    return this.keys().length;
  }

  keys(): string[] {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        keys.push(key.substring(this.prefix.length));
      }
    }
    return keys;
  }

  getStats(): CacheStats {
    return {
      totalRequests: 0,
      hits: 0,
      misses: 0,
      hitRate: 0,
      size: this.size(),
      maxSize: -1 // 本地存储没有固定大小限制
    };
  }

  private getKey(key: string): string {
    return this.prefix + key;
  }
}

// ==================== 缓存管理器 ====================

/**
 * 缓存管理器
 */
export class CacheManager {
  private caches = new Map<string, ICache>();

  /**
   * 注册缓存
   */
  register(name: string, cache: ICache): void {
    this.caches.set(name, cache);
  }

  /**
   * 获取缓存
   */
  getCache(name: string): ICache | undefined {
    return this.caches.get(name);
  }

  /**
   * 移除缓存
   */
  removeCache(name: string): boolean {
    return this.caches.delete(name);
  }

  /**
   * 清空所有缓存
   */
  clearAll(): void {
    this.caches.forEach(cache => cache.clear());
  }

  /**
   * 获取所有缓存名称
   */
  getCacheNames(): string[] {
    return Array.from(this.caches.keys());
  }
}

// ==================== 默认缓存实例 ====================

/**
 * 默认内存缓存实例
 */
export const defaultMemoryCache = new MemoryCache();

/**
 * 默认本地存储缓存实例
 */
export const defaultLocalStorageCache = new LocalStorageCache();

/**
 * 默认缓存管理器实例
 */
export const defaultCacheManager = new CacheManager();

// 注册默认缓存
defaultCacheManager.register('memory', defaultMemoryCache);
defaultCacheManager.register('localStorage', defaultLocalStorageCache);
